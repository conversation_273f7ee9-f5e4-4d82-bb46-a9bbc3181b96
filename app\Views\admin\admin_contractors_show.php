<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/contractors/' . $contractor['id'] . '/edit') ?>" class="btn btn-primary btn-mobile">
    <span class="btn-icon">✏️</span>
    <span class="btn-text">Edit Contractor</span>
</a>
<a href="<?= base_url('admin/contractors') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to List</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            <?= esc($contractor['name']) ?>
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Contractor Code: <strong><?= esc($contractor['contractor_code']) ?></strong>
            <?php
            $statusColors = [
                'active' => 'var(--brand-secondary)',
                'suspended' => 'var(--brand-warning)',
                'terminated' => 'var(--brand-danger)',
                'blacklisted' => '#DC2626'
            ];
            $statusColor = $statusColors[$contractor['status']] ?? 'var(--text-muted)';
            ?>
            <span style="display: inline-block; margin-left: var(--spacing-md); padding: var(--spacing-xs) var(--spacing-sm); background: <?= $statusColor ?>; color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                <?= esc($contractor['status']) ?>
            </span>
        </p>
    </div>
</div>

<!-- Overview Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Basic Info Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-weight: 700; color: white; font-size: 1.25rem;">
                <?= strtoupper(substr($contractor['name'], 0, 2)) ?>
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Basic Information</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Company details</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Business Type:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); text-transform: capitalize;"><?= esc($contractor['business_type']) ?></span>
            </div>
            <?php if ($contractor['registration_num']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Registration:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['registration_num']) ?></span>
            </div>
            <?php endif; ?>
            <?php if ($contractor['tax_id']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Tax ID:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['tax_id']) ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Contact Info Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                📞
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Contact Information</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Primary contact details</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <?php if ($contractor['contact_person']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Contact Person:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= esc($contractor['contact_person']) ?></span>
            </div>
            <?php endif; ?>
            <?php if ($contractor['contact_email']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Email:</span>
                <a href="mailto:<?= esc($contractor['contact_email']) ?>" style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= esc($contractor['contact_email']) ?></a>
            </div>
            <?php endif; ?>
            <?php if ($contractor['contact_phone']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Phone:</span>
                <a href="tel:<?= esc($contractor['contact_phone']) ?>" style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= esc($contractor['contact_phone']) ?></a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Performance Summary Card -->
    <div class="card" style="padding: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-lg); background: var(--gradient-accent); display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                📊
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary);">Performance Summary</div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Assessment overview</div>
            </div>
        </div>
        <div style="space-y: var(--spacing-sm);">
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Total Assessments:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm);"><?= $performance_summary['total_assessments'] ?></span>
            </div>
            <?php if ($performance_summary['average_scores']): ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Overall Score:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); color: var(--brand-primary);"><?= $performance_summary['average_scores']['overall'] ?>/5.0</span>
            </div>
            <?php endif; ?>
            <div style="margin-bottom: var(--spacing-sm);">
                <span style="color: var(--text-muted); font-size: 0.875rem;">Compliance Status:</span>
                <span style="font-weight: 600; margin-left: var(--spacing-sm); text-transform: capitalize;"><?= $compliance_status['overall_status'] ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Address Information -->
<?php if ($contractor['address_line1'] || $contractor['city'] || $contractor['state'] || $contractor['country_name']): ?>
<div class="card mb-xl">
    <div class="card-header">
        📍 Address Information
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="color: var(--text-primary); line-height: 1.6;">
            <?php if ($contractor['address_line1']): ?>
                <?= esc($contractor['address_line1']) ?><br>
            <?php endif; ?>
            <?php if ($contractor['address_line2']): ?>
                <?= esc($contractor['address_line2']) ?><br>
            <?php endif; ?>
            <?php if ($contractor['city'] || $contractor['state'] || $contractor['postal_code']): ?>
                <?= esc($contractor['city']) ?>
                <?php if ($contractor['state']): ?>, <?= esc($contractor['state']) ?><?php endif; ?>
                <?php if ($contractor['postal_code']): ?> <?= esc($contractor['postal_code']) ?><?php endif; ?><br>
            <?php endif; ?>
            <?php if ($contractor['country_name']): ?>
                <?= esc($contractor['country_name']) ?>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Services Offered -->
<?php if ($contractor['services_offered']): ?>
<div class="card mb-xl">
    <div class="card-header">
        🔧 Services & Capabilities
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="margin-bottom: var(--spacing-lg);">
            <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin-bottom: var(--spacing-sm);">Services Overview</h4>
            <p style="color: var(--text-secondary); line-height: 1.6; margin: 0;"><?= esc($contractor['services_offered']) ?></p>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Project Assignments -->
<div class="card mb-xl">
    <div class="card-header">
        📋 Project Assignments (<?= count($project_assignments ?? []) ?>)
    </div>
    <?php if (!empty($project_assignments)): ?>
        <div style="padding: var(--spacing-lg);">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: var(--spacing-lg);">
                <?php foreach ($project_assignments as $assignment): ?>
                    <div style="padding: var(--spacing-md); background: var(--bg-tertiary); border-radius: var(--radius-md); border-left: 4px solid <?= $assignment['is_active'] ? 'var(--brand-success)' : 'var(--brand-muted)' ?>;">
                        <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                            <span style="font-weight: 600; color: var(--text-primary);"><?= esc($assignment['project_title']) ?></span>
                            <?php if ($assignment['is_active']): ?>
                                <span style="padding: var(--spacing-xs); background: var(--brand-success); color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">ACTIVE</span>
                            <?php else: ?>
                                <span style="padding: var(--spacing-xs); background: var(--brand-muted); color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">INACTIVE</span>
                            <?php endif; ?>
                        </div>
                        <div style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                            Project Code: <?= esc($assignment['pro_code']) ?>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                            Joined: <?= date('M d, Y', strtotime($assignment['joined_at'])) ?>
                        </div>
                        <?php if ($assignment['client_flag'] && $assignment['client_flag'] !== 'neutral'): ?>
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">
                                Client Feedback:
                                <span style="color: <?= $assignment['client_flag'] === 'positive' ? 'var(--brand-success)' : 'var(--brand-danger)' ?>; font-weight: 600;">
                                    <?= ucfirst($assignment['client_flag']) ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        <?php if (!$assignment['is_active'] && $assignment['removal_reason']): ?>
                            <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                                Removal Reason: <?= esc($assignment['removal_reason']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 2rem; margin-bottom: var(--spacing-md); opacity: 0.5;">📋</div>
            <p style="color: var(--text-muted);">No project assignments yet.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Description -->
<?php if ($contractor['description']): ?>
<div class="card mb-xl">
    <div class="card-header">
        📝 Description
    </div>
    <div style="padding: var(--spacing-lg);">
        <p style="color: var(--text-secondary); line-height: 1.6; margin: 0;"><?= esc($contractor['description']) ?></p>
    </div>
</div>
<?php endif; ?>

<!-- Documents Section -->
<div class="card mb-xl">
    <div class="card-header">
        📄 Documents (<?= count($documents) ?>)
    </div>
    <?php if (!empty($documents)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>Document</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Expiry</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                        <tr>
                            <td>
                                <div style="font-weight: 600; color: var(--text-primary);"><?= esc($document['doc_title']) ?></div>
                                <?php if ($document['doc_number']): ?>
                                    <div style="color: var(--text-muted); font-size: 0.875rem;"><?= esc($document['doc_number']) ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="display: inline-block; padding: var(--spacing-xs) var(--spacing-sm); background: var(--bg-tertiary); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($document['doc_type']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($document['is_verified']): ?>
                                    <span style="color: var(--brand-secondary); font-weight: 600;">✅ Verified</span>
                                    <?php if ($document['verified_by_name']): ?>
                                        <div style="color: var(--text-muted); font-size: 0.75rem;">by <?= esc($document['verified_by_name']) ?></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--brand-warning); font-weight: 600;">⏳ Pending</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($document['expiry_date']): ?>
                                    <?php
                                    $expiryDate = new DateTime($document['expiry_date']);
                                    $today = new DateTime();
                                    $isExpired = $expiryDate < $today;
                                    $isExpiringSoon = $expiryDate->diff($today)->days <= 30 && !$isExpired;
                                    ?>
                                    <div style="color: <?= $isExpired ? 'var(--brand-danger)' : ($isExpiringSoon ? 'var(--brand-warning)' : 'var(--text-secondary)') ?>;">
                                        <?= $expiryDate->format('M d, Y') ?>
                                        <?php if ($isExpired): ?>
                                            <div style="font-size: 0.75rem; font-weight: 600;">EXPIRED</div>
                                        <?php elseif ($isExpiringSoon): ?>
                                            <div style="font-size: 0.75rem; font-weight: 600;">EXPIRING SOON</div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span style="color: var(--text-muted);">No expiry</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= base_url($document['doc_path']) ?>" target="_blank" class="btn btn-secondary btn-mobile" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    <span class="btn-icon">📄</span>
                                    <span class="btn-text">View</span>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 2rem; margin-bottom: var(--spacing-md); opacity: 0.5;">📄</div>
            <p style="color: var(--text-muted);">No documents uploaded yet.</p>
        </div>
    <?php endif; ?>
</div>

<!-- M&E Evaluations -->
<div class="card mb-xl">
    <div class="card-header">
        📊 M&E Evaluations (<?= count($evaluation_summary['performance_trend'] ?? []) ?>)
    </div>
    <?php if (!empty($evaluation_summary['performance_trend'])): ?>
        <div style="padding: var(--spacing-lg);">
            <!-- Latest Evaluation Summary -->
            <?php if ($evaluation_summary['latest_evaluation']): ?>
                <div style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: var(--bg-secondary); border-radius: var(--radius-md);">
                    <h4 style="color: var(--text-primary); font-size: 1rem; font-weight: 600; margin-bottom: var(--spacing-sm);">Latest Evaluation</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                        <div style="text-align: center;">
                            <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Quality</div>
                            <div style="font-weight: 600; color: var(--text-primary);"><?= ucfirst($evaluation_summary['latest_evaluation']['quality_rating'] ?? 'N/A') ?></div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Timeline</div>
                            <div style="font-weight: 600; color: var(--text-primary);"><?= ucfirst($evaluation_summary['latest_evaluation']['timeline_rating'] ?? 'N/A') ?></div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Budget</div>
                            <div style="font-weight: 600; color: var(--text-primary);"><?= ucfirst($evaluation_summary['latest_evaluation']['budget_rating'] ?? 'N/A') ?></div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: var(--spacing-xs);">Overall</div>
                            <div style="font-weight: 600; color: var(--text-primary);"><?= ucfirst($evaluation_summary['latest_evaluation']['overall_rating'] ?? 'N/A') ?></div>
                        </div>
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-muted);">
                        Evaluation Date: <?= date('M d, Y', strtotime($evaluation_summary['latest_evaluation']['evaluation_date'])) ?>
                        | Type: <?= ucfirst($evaluation_summary['latest_evaluation']['evaluation_type']) ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Evaluation History -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-lg);">
                <?php foreach ($evaluation_summary['performance_trend'] as $evaluation): ?>
                    <div style="padding: var(--spacing-md); background: var(--bg-tertiary); border-radius: var(--radius-md); border-left: 4px solid var(--brand-primary);">
                        <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: var(--spacing-sm);">
                            <span style="font-weight: 600; color: var(--text-primary);"><?= ucfirst($evaluation['evaluation_type']) ?> Evaluation</span>
                            <span style="padding: var(--spacing-xs); background: var(--brand-primary); color: white; border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                <?= ucfirst($evaluation['overall_rating'] ?? 'N/A') ?>
                            </span>
                        </div>
                        <div style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                            Date: <?= date('M d, Y', strtotime($evaluation['evaluation_date'])) ?>
                        </div>
                        <?php if ($evaluation['findings']): ?>
                            <div style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                                <strong>Findings:</strong> <?= esc(substr($evaluation['findings'], 0, 100)) ?><?= strlen($evaluation['findings']) > 100 ? '...' : '' ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($evaluation['action_required']): ?>
                            <div style="color: var(--brand-danger); font-size: 0.875rem; font-weight: 600;">
                                ⚠️ Action Required
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 2rem; margin-bottom: var(--spacing-md); opacity: 0.5;">📊</div>
            <p style="color: var(--text-muted);">No M&E evaluations conducted yet.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Mobile-friendly CSS -->
<style>
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .btn-mobile {
        min-height: 48px;
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .table-container {
        overflow-x: auto;
    }
}
</style>

<?= $this->endSection() ?>
