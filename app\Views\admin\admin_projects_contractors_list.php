<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-contractors" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary">
    ➕ Assign Contractor
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Contractors
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage contractor assignments for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                📁
            </div>
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <?= esc($project['title']) ?>
                </h3>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contractors Summary -->
<div class="card mb-xl">
    <div class="card-header">
        📊 Contractors Summary
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $stats['total_contractors'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Contractors</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $stats['active_contractors'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Active Contractors</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_client_flag']['positive'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Positive Rating</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-warning); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_client_flag']['neutral'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Neutral Rating</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-danger); margin-bottom: var(--spacing-xs);">
                    <?= $stats['by_client_flag']['negative'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Negative Rating</div>
            </div>
        </div>
    </div>
</div>

<!-- Contractors List -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>🏗️</span>
                <span>Assigned Contractors</span>
            </div>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary" style="margin-left: auto;">
                ➕ Assign New Contractor
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($contractors)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php foreach ($contractors as $contractor): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); border-left: 4px solid <?= $contractor['is_active'] ? 'var(--brand-success)' : 'var(--brand-muted)' ?>;">
                        <div style="display: flex; align-items: start; justify-content: between; gap: var(--spacing-md);">
                            <!-- Contractor Info -->
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                                    <h4 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                                        <?= esc($contractor['contractor_name']) ?>
                                    </h4>
                                    <?php if ($contractor['is_active']): ?>
                                        <span style="background: var(--brand-success); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                            ✅ Active
                                        </span>
                                    <?php else: ?>
                                        <span style="background: var(--brand-muted); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                            ❌ Removed
                                        </span>
                                    <?php endif; ?>
                                    
                                    <?php if ($contractor['is_active']): ?>
                                        <span style="background: <?= $contractor['client_flag'] === 'positive' ? 'var(--brand-success)' : ($contractor['client_flag'] === 'negative' ? 'var(--brand-danger)' : 'var(--brand-warning)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                            <?php if ($contractor['client_flag'] === 'positive'): ?>
                                                👍 Positive
                                            <?php elseif ($contractor['client_flag'] === 'negative'): ?>
                                                👎 Negative
                                            <?php else: ?>
                                                ➖ Neutral
                                            <?php endif; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                                    <span style="color: var(--text-muted); font-weight: 500;">Organization Code:</span>
                                    <span style="color: var(--text-secondary); font-family: monospace;"><?= esc($contractor['contractor_code']) ?></span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Joined:</span>
                                    <span style="color: var(--text-secondary);"><?= date('M j, Y', strtotime($contractor['joined_at'])) ?></span>

                                    <?php if (!$contractor['is_active'] && $contractor['removal_reason']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">Removal Reason:</span>
                                        <span style="color: var(--text-secondary);"><?= esc($contractor['removal_reason']) ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Contractor Actions -->
                            <?php if ($contractor['is_active']): ?>
                                <div style="display: flex; gap: var(--spacing-sm);">
                                    <button onclick="showRemoveContractorModal(<?= $contractor['contractor_id'] ?>, '<?= esc($contractor['contractor_name']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        🗑️ Remove
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🏗️</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Contractors Assigned</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start building your project team by assigning the first contractor.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary">
                    ➕ Assign First Contractor
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Remove Contractor Modal -->
<div id="removeContractorModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Remove Project Contractor</h3>
        
        <form id="removeContractorForm" method="POST">
            <?= csrf_field() ?>
            <div style="margin-bottom: var(--spacing-lg);">
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-md);">
                    Are you sure you want to remove <strong id="contractorName"></strong> from this project?
                </p>
                <p style="color: var(--text-muted); font-size: 0.875rem;">
                    This action will mark the contractor as inactive but preserve the assignment history.
                </p>
            </div>

            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Removal Reason <span style="color: var(--brand-danger);">*</span>
                </label>
                <textarea name="removal_reason" rows="3" required style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-danger); border-radius: var(--radius-md); font-family: inherit; resize: vertical;" placeholder="Enter reason for removing this contractor..."></textarea>
            </div>

            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
                <button type="button" onclick="hideRemoveContractorModal()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="btn btn-danger">
                    🗑️ Remove Contractor
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function showRemoveContractorModal(contractorId, contractorName) {
    document.getElementById('contractorName').textContent = contractorName;
    document.getElementById('removeContractorForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/contractors/') ?>' + contractorId + '/remove';
    document.getElementById('removeContractorModal').style.display = 'flex';
}

function hideRemoveContractorModal() {
    document.getElementById('removeContractorModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('removeContractorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideRemoveContractorModal();
    }
});
</script>

<?= $this->endSection() ?>
