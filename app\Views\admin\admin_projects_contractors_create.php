<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-secondary">
    ← Back to Contractors
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Assign Project Contractor
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Assign a new contractor to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                📁
            </div>
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <?= esc($project['title']) ?>
                </h3>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Contractors Summary -->
<?php if (!empty($currentContractors)): ?>
<div class="card mb-xl">
    <div class="card-header">
        🏗️ Currently Assigned Contractors
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; gap: var(--spacing-md);">
            <?php foreach ($currentContractors as $contractor): ?>
                <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-md);">
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--text-primary);">
                            <?= esc($contractor['contractor_name']) ?>
                        </div>
                        <div style="font-size: 0.875rem; color: var(--text-muted);">
                            Code: <?= esc($contractor['contractor_code']) ?> • Joined: <?= date('M j, Y', strtotime($contractor['joined_at'])) ?>
                        </div>
                    </div>
                    <span style="background: <?= $contractor['client_flag'] === 'positive' ? 'var(--brand-success)' : ($contractor['client_flag'] === 'negative' ? 'var(--brand-danger)' : 'var(--brand-warning)') ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                        <?= esc($contractor['client_flag']) ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Assignment Form -->
<div class="card mb-xl">
    <div class="card-header">
        ➕ Assign New Contractor
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($availableOrganizations)): ?>
            <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>">
                <?= csrf_field() ?>
                
                <!-- Organization Selection -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Select Organization <span style="color: var(--brand-danger);">*</span>
                    </label>
                    <select name="contractor_id" required style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-danger); border-radius: var(--radius-md); font-family: inherit; background: white;">
                        <option value="">Choose an organization...</option>
                        <?php foreach ($availableOrganizations as $org): ?>
                            <option value="<?= $org['id'] ?>" <?= old('contractor_id') == $org['id'] ? 'selected' : '' ?>>
                                <?= esc($org['name']) ?> (<?= esc($org['org_code']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                        Only active organizations are shown
                    </div>
                </div>

                <!-- Join Date -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Assignment Date <span style="color: var(--brand-danger);">*</span>
                    </label>
                    <input type="date" name="joined_at" value="<?= old('joined_at') ?: date('Y-m-d') ?>" required style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-danger); border-radius: var(--radius-md); font-family: inherit;">
                    <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                        Date when the contractor was assigned to this project
                    </div>
                </div>

                <!-- Client Flag -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Initial Client Rating
                    </label>
                    
                    <div style="display: grid; gap: var(--spacing-md);">
                        <!-- Positive Rating -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="client_flag" value="positive" <?= old('client_flag') == 'positive' ? 'checked' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        👍 Positive
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Organization has a good track record and is recommended for this project.
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Neutral Rating -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="client_flag" value="neutral" <?= old('client_flag') == 'neutral' || !old('client_flag') ? 'checked' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        ➖ Neutral
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Standard assignment with no specific performance expectations or concerns.
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Negative Rating -->
                        <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                            <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                                <input type="radio" name="client_flag" value="negative" <?= old('client_flag') == 'negative' ? 'checked' : '' ?> style="margin-top: var(--spacing-xs);">
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        👎 Negative
                                    </div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                        Organization has performance concerns or requires close monitoring.
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-md); margin-top: var(--spacing-md);">
                        <div style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 600;">
                            ℹ️ Client Rating Information
                        </div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: var(--spacing-xs);">
                            This rating can be updated later based on contractor performance during the project.
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        ➕ Assign Contractor
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🏗️</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Available Organizations</h3>
                <p style="margin-bottom: var(--spacing-lg);">
                    All active organizations are already assigned to this project, or there are no active organizations available.
                </p>
                <div style="display: flex; gap: var(--spacing-md); justify-content: center;">
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-primary">
                        ← Back to Contractors
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Form validation
document.querySelector('form')?.addEventListener('submit', function(e) {
    const contractorId = document.querySelector('select[name="contractor_id"]').value;
    const joinedAt = document.querySelector('input[name="joined_at"]').value;
    
    if (!contractorId) {
        e.preventDefault();
        alert('Please select an organization to assign as contractor.');
        return;
    }
    
    if (!joinedAt) {
        e.preventDefault();
        alert('Please select an assignment date.');
        return;
    }
});
</script>

<?= $this->endSection() ?>
